import React, { useState, useEffect } from "react";
import { IoSearchOutline } from "react-icons/io5";
import { IoIosArrowDown } from "react-icons/io";
import { LuAlignLeft } from "react-icons/lu";
import MkdSDK from "Utils/MkdSDK";

let sdk = new MkdSDK();

const StaffInvoices = () => {
  const [expandedItems, setExpandedItems] = useState({});
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedDate, setSelectedDate] = useState("09/2024");
  const [sortType, setSortType] = useState("date");
  const [invoices, setInvoices] = useState([]);
  const [loading, setLoading] = useState(false);
  const [invoiceData, setInvoiceData] = useState(null);
  const [selectedInvoiceType, setSelectedInvoiceType] = useState("all");

  const toggleItem = (id) => {
    setExpandedItems((prev) => ({
      ...prev,
      [id]: !prev[id],
    }));
  };

  const fetchInvoices = async () => {
    setLoading(true);
    try {
      const response = await sdk.callRawAPI(
        `/v3/api/custom/courtmatchup/staff/reservations/billing/staff-invoices`,
        {},
        "GET"
      );
      setInvoiceData(response);
      setInvoices(response.invoices || []);
    } catch (error) {
      console.log(error);
    } finally {
      setLoading(false);
    }
  };

  // Filter invoices based on search query and invoice type
  const filteredInvoices = invoices.filter((invoice) => {
    const matchesSearch =
      invoice.user_first_name
        ?.toLowerCase()
        .includes(searchQuery.toLowerCase()) ||
      invoice.user_last_name
        ?.toLowerCase()
        .includes(searchQuery.toLowerCase()) ||
      invoice.receipt_id?.toLowerCase().includes(searchQuery.toLowerCase()) ||
      invoice.status?.toLowerCase().includes(searchQuery.toLowerCase());

    const matchesType =
      selectedInvoiceType === "all" ||
      invoice.invoice_type?.toLowerCase() === selectedInvoiceType.toLowerCase();

    return matchesSearch && matchesType;
  });

  // Format currency
  const formatCurrency = (amount, currency = "usd") => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: currency.toUpperCase(),
    }).format(amount);
  };

  // Format date
  const formatDate = (dateString) => {
    if (!dateString) return "N/A";
    const date = new Date(dateString);
    return date.toLocaleDateString("en-US", {
      month: "2-digit",
      day: "2-digit",
      year: "2-digit",
    });
  };

  useEffect(() => {
    fetchInvoices();
  }, []);

  if (loading) {
    return (
      <div className="flex w-full items-center justify-center py-8">
        <div className="text-lg">Loading invoices...</div>
      </div>
    );
  }

  return (
    <div className="w-full">
      <div className="mb-6 flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
        <h1 className="text-2xl font-semibold">My invoices</h1>
        <div className="relative inline-block">
          <select
            className="appearance-none rounded-lg border border-gray-200 bg-white px-4 py-2 pr-8 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
            value={selectedInvoiceType}
            onChange={(e) => setSelectedInvoiceType(e.target.value)}
          >
            <option value="all">Type: All</option>
            <option value="Staff">Staff</option>
            <option value="Subscription">Subscription</option>
            <option value="Payment">Payment</option>
          </select>
        </div>
      </div>

      <div className="mb-6 flex flex-col gap-3 sm:flex-row">
        <div className="relative flex-1">
          <IoSearchOutline className="absolute left-3 top-1/2 -translate-y-1/2 transform text-gray-400" />
          <input
            type="text"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            placeholder="Search invoices..."
            className="w-full rounded-lg border border-gray-200 py-2 pl-10 pr-4 focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        </div>
        <div className="relative w-full sm:w-auto">
          <select
            className="w-full rounded-lg border border-gray-200 px-4 py-2 sm:w-auto"
            value={selectedDate}
            onChange={(e) => setSelectedDate(e.target.value)}
          >
            <option value="09/2024">09/2024</option>
            <option value="08/2024">08/2024</option>
            <option value="07/2024">07/2024</option>
          </select>
        </div>
        <button
          onClick={() =>
            setSortType((prev) => (prev === "date" ? "amount" : "date"))
          }
          className="flex w-full items-center justify-center gap-2 rounded-lg border border-gray-200 px-4 py-2 hover:bg-gray-50 sm:w-auto"
        >
          BY date
          <LuAlignLeft className="transform" />
        </button>
      </div>

      <div className="space-y-4">
        {filteredInvoices.length === 0 ? (
          <div className="py-8 text-center text-gray-500">
            No invoices found.
          </div>
        ) : (
          filteredInvoices.map((invoice) => (
            <div
              key={invoice.id}
              className="rounded-lg border border-gray-100 bg-gray-50 p-3 shadow-sm"
            >
              <button
                onClick={() => toggleItem(invoice.id)}
                className="flex w-full flex-col gap-2 sm:flex-row sm:items-center sm:justify-between"
              >
                <div className="flex items-center gap-2">
                  <IoIosArrowDown
                    className={`transform transition-transform ${
                      expandedItems[invoice.id] ? "" : "-rotate-90"
                    }`}
                  />
                  <span className="text-sm sm:text-base">
                    {invoice.invoice_type || invoice.type}
                  </span>
                  {invoice.status && (
                    <span
                      className={`rounded-full px-2 py-1 text-xs ${
                        invoice.status === "completed"
                          ? "bg-green-100 text-green-800"
                          : "bg-yellow-100 text-yellow-800"
                      }`}
                    >
                      {invoice.status}
                    </span>
                  )}
                </div>
                <div className="flex items-center justify-between gap-4 pl-6 sm:pl-0">
                  <span className="text-sm text-gray-600">
                    {formatDate(invoice.date)}
                  </span>
                  <span className="font-medium">
                    {formatCurrency(invoice.amount, invoice.currency)}
                  </span>
                </div>
              </button>
              {expandedItems[invoice.id] && (
                <div className="mt-4 space-y-3 border-t border-gray-200 p-4">
                  {invoice.receipt_id && (
                    <div className="flex justify-between">
                      <span className="text-gray-600">Receipt ID</span>
                      <span>{invoice.receipt_id}</span>
                    </div>
                  )}
                  {invoice.user_first_name && invoice.user_last_name && (
                    <div className="flex justify-between">
                      <span className="text-gray-600">Customer</span>
                      <span>
                        {invoice.user_first_name} {invoice.user_last_name}
                      </span>
                    </div>
                  )}
                  <div className="flex justify-between">
                    <span className="text-gray-600">Created on</span>
                    <span>{formatDate(invoice.create_at)}</span>
                  </div>
                  {invoice.total_amount &&
                    invoice.total_amount !== invoice.amount && (
                      <div className="flex justify-between">
                        <span className="text-gray-600">Total amount</span>
                        <span>
                          {formatCurrency(
                            invoice.total_amount,
                            invoice.currency
                          )}
                        </span>
                      </div>
                    )}
                  {invoice.valid_until && (
                    <div className="flex justify-between">
                      <span className="text-gray-600">Valid until</span>
                      <span>{formatDate(invoice.valid_until)}</span>
                    </div>
                  )}
                  {invoice.club_name && (
                    <div className="flex justify-between">
                      <span className="text-gray-600">Club</span>
                      <span>{invoice.club_name}</span>
                    </div>
                  )}
                  <div className="flex justify-between">
                    <span className="text-gray-600">Payment method</span>
                    <span>{invoice.payment_method}</span>
                  </div>
                  {invoice.reservation_type && (
                    <div className="flex justify-between">
                      <span className="text-gray-600">Reservation type</span>
                      <span>{invoice.reservation_type}</span>
                    </div>
                  )}
                  <button
                    className="mt-2 rounded-lg border border-gray-200 px-4 py-2 text-sm hover:bg-gray-50"
                    onClick={() => window.print()}
                  >
                    Print receipt
                  </button>
                </div>
              )}
            </div>
          ))
        )}
      </div>
    </div>
  );
};

export default StaffInvoices;
