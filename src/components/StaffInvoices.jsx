import React, { useState, useEffect } from "react";
import { IoSearchOutline } from "react-icons/io5";
import { IoIosArrowDown } from "react-icons/io";
import { LuAlignLeft } from "react-icons/lu";
import MkdSDK from "Utils/MkdSDK";

let sdk = new MkdSDK();

const StaffInvoices = () => {
  const [expandedItems, setExpandedItems] = useState({});
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedDate, setSelectedDate] = useState("09/2024");
  const [sortType, setSortType] = useState("date");

  const toggleItem = (id) => {
    setExpandedItems((prev) => ({
      ...prev,
      [id]: !prev[id],
    }));
  };
  const fetchInvoices = async () => {
    try {
      const response = await sdk.callRawAPI(
        `/v3/api/custom/courtmatchup/staff/reservations/billing/staff-invoices`,
        {},
        "GET"
      );
      setInvoices(response.invoices);
    } catch (error) {
      console.log(error);
    }
  };

  useEffect(() => {
    fetchInvoices();
  }, []);

  // Mock data - replace with actual API call
  const invoices = [
    {
      id: 1,
      type: "Payment",
      date: "01/12/24",
      amount: 4.99,
      amount_paid: 55.25,
      purchased_on: "01/01/2024",
      reservation_time: "01/01/2024 08:00 AM - 9:00 AM",
      payment_method: "Credit card •••• 1234",
    },
    {
      id: 2,
      type: "Subscription",
      date: "01/12/24",
      amount: 4.99,
      plan_price: 4.99,
      purchased_on: "01/01/2024",
      valid_until: "01/01/2025",
      subscription_id: "#1234567890",
      payment_method: "Credit card •••• 1234",
    },
  ];

  return (
    <div className="w-full">
      <div className="mb-6 flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
        <h1 className="text-2xl font-semibold">My invoices</h1>
        <div className="relative inline-block">
          <select
            className="appearance-none rounded-lg border border-gray-200 bg-white px-4 py-2 pr-8 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
            defaultValue="All"
          >
            <option>Type:All</option>
            <option>Subscription</option>
            <option>Payment</option>
          </select>
        </div>
      </div>

      <div className="mb-6 flex flex-col gap-3 sm:flex-row">
        <div className="relative flex-1">
          <IoSearchOutline className="absolute left-3 top-1/2 -translate-y-1/2 transform text-gray-400" />
          <input
            type="text"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            placeholder="Search invoices..."
            className="w-full rounded-lg border border-gray-200 py-2 pl-10 pr-4 focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        </div>
        <div className="relative w-full sm:w-auto">
          <select
            className="w-full rounded-lg border border-gray-200 px-4 py-2 sm:w-auto"
            value={selectedDate}
            onChange={(e) => setSelectedDate(e.target.value)}
          >
            <option value="09/2024">09/2024</option>
            <option value="08/2024">08/2024</option>
            <option value="07/2024">07/2024</option>
          </select>
        </div>
        <button
          onClick={() =>
            setSortType((prev) => (prev === "date" ? "amount" : "date"))
          }
          className="flex w-full items-center justify-center gap-2 rounded-lg border border-gray-200 px-4 py-2 hover:bg-gray-50 sm:w-auto"
        >
          BY date
          <LuAlignLeft className="transform" />
        </button>
      </div>

      <div className="space-y-4">
        {invoices.map((invoice) => (
          <div
            key={invoice.id}
            className="rounded-lg border border-gray-100 bg-gray-50 p-3 shadow-sm"
          >
            <button
              onClick={() => toggleItem(invoice.id)}
              className="flex w-full flex-col gap-2 sm:flex-row sm:items-center sm:justify-between"
            >
              <div className="flex items-center gap-2">
                <IoIosArrowDown
                  className={`transform transition-transform ${
                    expandedItems[invoice.id] ? "" : "-rotate-90"
                  }`}
                />
                <span className="text-sm sm:text-base">{invoice.type}</span>
              </div>
              <div className="flex items-center justify-between gap-4 pl-6 sm:pl-0">
                <span className="text-sm text-gray-600">{invoice.date}</span>
                <span className="font-medium">${invoice.amount}</span>
              </div>
            </button>
            {expandedItems[invoice.id] && (
              <div className="mt-4 space-y-3 border-t border-gray-200 p-4">
                {invoice.amount_paid && (
                  <div className="flex justify-between">
                    <span className="text-gray-600">Amount paid</span>
                    <span>${invoice.amount_paid}</span>
                  </div>
                )}
                <div className="flex justify-between">
                  <span className="text-gray-600">Purchased on</span>
                  <span>{invoice.purchased_on}</span>
                </div>
                {invoice.reservation_time && (
                  <div className="flex justify-between">
                    <span className="text-gray-600">Reservation time</span>
                    <span>{invoice.reservation_time}</span>
                  </div>
                )}
                {invoice.valid_until && (
                  <div className="flex justify-between">
                    <span className="text-gray-600">Valid until</span>
                    <span>{invoice.valid_until}</span>
                  </div>
                )}
                {invoice.subscription_id && (
                  <div className="flex justify-between">
                    <span className="text-gray-600">Subscription ID</span>
                    <span>{invoice.subscription_id}</span>
                  </div>
                )}
                <div className="flex justify-between">
                  <span className="text-gray-600">Payment method</span>
                  <span>{invoice.payment_method}</span>
                </div>
                <button className="mt-2 rounded-lg border border-gray-200 px-4 py-2 text-sm hover:bg-gray-50">
                  Print receipt
                </button>
              </div>
            )}
          </div>
        ))}
      </div>
    </div>
  );
};

export default StaffInvoices;
